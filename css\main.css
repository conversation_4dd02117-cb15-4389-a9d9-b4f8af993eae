/* <PERSON><PERSON> Poster Maker - Main Styles */
/* 主要樣式文件 */

:root {
    --color-1: rgb(0, 200, 255);
    --color-2: rgb(255, 200, 0);
    --color-3: rgb(47, 255, 92);
    --color-4: rgb(255, 49, 49);
    --color-5: rgb(0, 255, 234);
    --border-color-1: rgb(0, 162, 255);
    --border-color-2: rgb(255, 180, 19);
    --border-color-3: rgb(30, 255, 0);
    --border-color-4: rgb(255, 63, 63);
    --border-color-5: rgb(12, 255, 243);
    --text-color-1: rgb(0, 0, 0);
    --text-color-2: rgb(255, 255, 255);
    --text-color-3: rgb(0, 255, 128);
    --text-color-4: rgb(255, 128, 0);
    --text-color-5: rgb(255, 0, 128);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    overflow-x: hidden;
}

.kms-container {
    display: flex;
    height: 100vh;
    max-width: 2000px;
    margin: 0 auto;
}

/* Header */
.kms-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.kms-logo {
    font-size: 1.5rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.kms-logo::before {
    content: "🎨";
    font-size: 1.8rem;
}

.kms-language-toggle {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.kms-language-toggle:hover {
    background: rgba(255,255,255,0.3);
}

/* Fixed Print Button */
.kms-fixed-print-btn {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 1001;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    border-radius: 12px;
}

.kms-fixed-print-btn .kms-print-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    font-size: 1.1rem;
    padding: 1rem 1.5rem;
    font-weight: 600;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    transition: all 0.3s ease;
}

.kms-fixed-print-btn .kms-print-btn:hover {
    background: linear-gradient(135deg, #218838, #1ea080);
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(40, 167, 69, 0.4);
}

/* Main Content Area */
.kms-main {
    display: flex !important;
    margin-top: 70px;
    height: calc(100vh - 70px);
    max-width: 2000px;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    flex-wrap: nowrap;
}

/* Sidebar */
.kms-sidebar {
    width: 576px;
    background: white;
    border-right: 1px solid #e0e0e0;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0,0,0,0.05);
    flex-shrink: 0;
}

/* Canvas Area */
.kms-canvas-area {
    flex: 1 1 auto !important;
    display: flex !important;
    flex-direction: column;
    background: #f8f9fa;
    position: relative;
    min-width: 600px;
    max-width: none;
    height: 100%;
    overflow: hidden;
}

/* Right Panel */
.kms-right-panel {
    width: 300px;
    background: white;
    border-left: 1px solid #e0e0e0;
    overflow-y: auto;
    box-shadow: -2px 0 10px rgba(0,0,0,0.05);
    flex-shrink: 0;
    padding: 1rem;
}

.kms-right-section {
    margin-bottom: 2rem;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.kms-right-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.kms-right-section-title i {
    color: #667eea;
    font-size: 1.2rem;
}

/* Utility Classes */
.kms-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.kms-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.kms-btn-secondary {
    background: #6c757d;
}

.kms-btn-secondary:hover {
    background: #5a6268;
}

.kms-btn-success {
    background: #28a745;
}

.kms-btn-success:hover {
    background: #218838;
}

.kms-btn-danger {
    background: #dc3545;
}

.kms-btn-danger:hover {
    background: #c82333;
}

.kms-input {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
    width: 100%;
}

.kms-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.kms-select {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 0.9rem;
    background: white;
    cursor: pointer;
    width: 100%;
}

.kms-select:focus {
    outline: none;
    border-color: #667eea;
}

/* Form Groups */
.kms-form-group {
    margin-bottom: 1.5rem;
}

.kms-form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

/* Color Picker */
.kms-color-picker {
    width: 50px;
    height: 40px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    background: none;
    padding: 0;
}

/* Range Slider */
.kms-range {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e0e0e0;
    outline: none;
    -webkit-appearance: none;
}

.kms-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.kms-range::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .kms-sidebar {
        width: 400px;
    }
    
    .kms-canvas-area {
        min-width: 500px;
    }
}

@media (max-width: 768px) {
    .kms-sidebar {
        width: 350px;
    }
    
    .kms-canvas-area {
        min-width: 400px;
    }
    
    .kms-header {
        padding: 1rem;
    }
    
    .kms-logo {
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .kms-sidebar {
        position: fixed;
        left: -540px;
        top: 70px;
        height: calc(100vh - 70px);
        z-index: 999;
        transition: left 0.3s ease;
        width: 540px;
    }
    
    .kms-sidebar.active {
        left: 0;
    }
    
    .kms-canvas-area {
        width: 100%;
    }
}
