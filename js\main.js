/**
 * KMS Poster Maker - Main Application Controller
 * 主應用程序控制器
 */

class KMSPosterMaker {
    constructor() {
        this.currentLanguage = 'en';
        this.currentPaperSize = 'letter';
        this.selectedElement = null;
        this.elements = [];
        this.elementCounter = 0;
        this.isDragging = false;
        this.dragOffset = { x: 0, y: 0 };
        this.layerManager = null;
        
        this.translations = {
            en: {
                title: 'KMS Poster Maker',
                paperSize: 'Paper Size',
                letter: 'Letter (8.5" × 11")',
                size4x6: '4" × 6"',
                addElements: 'Add Elements',
                addText: 'Add Text',
                addImage: 'Add Image',
                addQR: 'Add QR Code',
                background: 'Background',
                borders: 'Canvas Borders',
                print: 'Print Poster',
                textControls: 'Text Controls',
                imageControls: 'Image Controls',
                qrControls: 'QR Code Controls',
                font: 'Font',
                fontSize: 'Font Size',
                fontColor: 'Font Color',
                textBorder: 'Text Border',
                borderWidth: 'Border Width',
                borderColor: 'Border Color',
                borderRadius: 'Border Radius',
                enterUrl: 'Enter URL for QR Code',
                generate: 'Generate QR Code',
                uploadImage: 'Click to upload or drag image here',
                supportedFormats: 'Supported: JPG, PNG, GIF',
                // File Management
                savePoster: 'Save Poster',
                loadPoster: 'Load Poster',
                manageSaved: 'Manage Saved',
                posterName: 'Poster Name',
                enterPosterName: 'Enter poster name',
                save: 'Save',
                cancel: 'Cancel',
                load: 'Load',
                export: 'Export',
                delete: 'Delete',
                import: 'Import',
                selectFile: 'Select File',
                importFromFile: 'Import from File',
                exportAsJSON: 'Export as JSON',
                savedPosters: 'Saved Posters',
                noSavedPosters: 'No saved posters found',
                confirmDelete: 'Are you sure you want to delete this poster?',
                confirmClearAll: 'Are you sure you want to delete ALL saved posters? This action cannot be undone.',
                allPostersDeleted: 'All posters have been deleted.',
                posterSaved: 'Poster saved successfully!',
                posterLoaded: 'Poster loaded successfully!',
                posterDeleted: 'Poster deleted successfully!',
                posterExported: 'Poster exported successfully!',
                posterImported: 'Poster imported successfully!',
                errorSaving: 'Error saving poster',
                errorLoading: 'Error loading poster',
                errorDeleting: 'Error deleting poster',
                errorExporting: 'Error exporting poster',
                errorImporting: 'Error importing poster',
                selectJSONFile: 'Please select a JSON file to import.',
                quickColors: 'Quick Colors',
                applyingTo: 'Applying to:',
                textColor: 'Text Color',
                textBorder: 'Text Border',
                textBackground: 'Text Background',
                textShadow: 'Text Shadow',
                imageBorder: 'Image Border',
                invalidJSONFile: 'Invalid JSON file format.',
                noPreview: 'No Preview',
                paper: 'Paper',
                elements: 'Elements',
                total: 'Total',
                posters: 'posters',
                clearAll: 'Clear All',
                close: 'Close',
                noSavedPostersMessage: 'You haven\'t saved any posters yet.',
                createPosterMessage: 'Create a poster and click "Save Poster" to get started!',
                ok: 'OK'
            },
            zh: {
                title: 'KMS 海報製作器',
                paperSize: '紙張尺寸',
                letter: 'Letter (8.5" × 11")',
                size4x6: '4" × 6"',
                addElements: '添加元素',
                addText: '添加文字',
                addImage: '添加圖片',
                addQR: '添加二維碼',
                background: '背景設置',
                borders: '畫布邊框',
                print: '打印海報',
                textControls: '文字控制',
                imageControls: '圖片控制',
                qrControls: '二維碼控制',
                font: '字體',
                fontSize: '字體大小',
                fontColor: '字體顏色',
                textBorder: '文字邊框',
                borderWidth: '邊框寬度',
                borderColor: '邊框顏色',
                borderRadius: '邊框圓角',
                enterUrl: '輸入二維碼網址',
                generate: '生成二維碼',
                uploadImage: '點擊上傳或拖拽圖片到此處',
                supportedFormats: '支持格式：JPG, PNG, GIF',
                // File Management
                savePoster: '儲存海報',
                loadPoster: '載入海報',
                manageSaved: '管理已儲存',
                posterName: '海報名稱',
                enterPosterName: '請輸入海報名稱',
                save: '儲存',
                cancel: '取消',
                load: '載入',
                export: '匯出',
                delete: '刪除',
                import: '匯入',
                selectFile: '選擇檔案',
                importFromFile: '從檔案匯入',
                exportAsJSON: '匯出為JSON',
                savedPosters: '已儲存的海報',
                noSavedPosters: '未找到已儲存的海報',
                confirmDelete: '確定要刪除此海報嗎？',
                confirmClearAll: '您確定要刪除所有已儲存的海報嗎？此操作無法撤銷。',
                allPostersDeleted: '所有海報已被刪除。',
                posterSaved: '海報儲存成功！',
                posterLoaded: '海報載入成功！',
                posterDeleted: '海報刪除成功！',
                posterExported: '海報匯出成功！',
                posterImported: '海報匯入成功！',
                errorSaving: '儲存海報時發生錯誤',
                errorLoading: '載入海報時發生錯誤',
                errorDeleting: '刪除海報時發生錯誤',
                errorExporting: '匯出海報時發生錯誤',
                errorImporting: '匯入海報時發生錯誤',
                selectJSONFile: '請選擇要匯入的JSON檔案。',
                quickColors: '快速顏色',
                applyingTo: '應用到：',
                textColor: '文字顏色',
                textBorder: '文字邊框',
                textBackground: '文字背景',
                textShadow: '文字陰影',
                imageBorder: '圖片邊框',
                invalidJSONFile: 'JSON檔案格式無效。',
                noPreview: '無預覽',
                paper: '紙張',
                elements: '元素',
                total: '總計',
                posters: '個海報',
                clearAll: '清除全部',
                close: '關閉',
                noSavedPostersMessage: '您還沒有儲存任何海報。',
                createPosterMessage: '創建一個海報並點擊「儲存海報」開始使用！',
                ok: '確定'
            }
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.updateLanguage();
        this.setupCanvas();
        this.loadFonts();
        this.initializeLayerManager();
        this.initializePrintHandler();
        this.initializeImageHandler();
        this.initializeQRGenerator();
        this.initializeQuickColors();
        this.initializeTextHandler();
        this.initializeEnhancedControls();
    }
    
    initializePrintHandler() {
        this.printHandler = new KMSPrintHandler(this);
    }
    
    initializeImageHandler() {
        this.imageHandler = new KMSImageHandler(this);
    }
    
    initializeQRGenerator() {
        this.qrGenerator = new KMSQRGenerator(this);
    }

    initializeTextHandler() {
        this.textHandler = new KMSTextHandler(this);
    }

    initializeQuickColors() {
        // Setup color target buttons
        const colorTargetButtons = document.querySelectorAll('.kms-color-target-btn');
        colorTargetButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const target = e.currentTarget.dataset.target;
                this.setQuickColorTarget(target);
                
                // Update active state
                colorTargetButtons.forEach(btn => btn.classList.remove('active'));
                e.currentTarget.classList.add('active');
            });
        });

        // Setup quick color buttons
        const quickColorButtons = document.querySelectorAll('.kms-quick-color-btn');
        quickColorButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const color = e.currentTarget.dataset.color;
                this.applyQuickColor(color);
                
                // Update active state
                quickColorButtons.forEach(btn => btn.classList.remove('active'));
                e.currentTarget.classList.add('active');
            });
        });

        // Initialize with default target
        this.currentQuickColorTarget = 'text';
        this.updateQuickColorDisplay();
    }

    setQuickColorTarget(target) {
        this.currentQuickColorTarget = target;
        this.updateQuickColorDisplay();
    }

    updateQuickColorDisplay() {
        const targetLabel = document.querySelector('.kms-current-target-label i');
        const targetName = document.querySelector('.kms-current-target-name');
        
        if (!targetLabel || !targetName) return;

        const targetConfig = {
            text: { icon: 'fas fa-font', key: 'textColor' },
            border: { icon: 'fas fa-border-style', key: 'textBorder' },
            background: { icon: 'fas fa-fill', key: 'textBackground' },
            shadow: { icon: 'fas fa-shadow', key: 'textShadow' },
            imageBorder: { icon: 'fas fa-image', key: 'imageBorder' }
        };

        const config = targetConfig[this.currentQuickColorTarget];
        if (config) {
            targetLabel.className = config.icon;
            
            // Update text based on current language
            const translations = this.translations[this.currentLanguage];
            if (translations[config.key]) {
                targetName.textContent = translations[config.key];
            }
        }
    }
    
    applyQuickColor(color) {
        if (!this.selectedElement) return;
        
        const target = this.currentQuickColorTarget;
        
        if (this.selectedElement.classList.contains('kms-text-element')) {
            switch (target) {
                case 'text':
                    this.selectedElement.style.color = color;
                    const fontColorPicker = document.getElementById('fontColor');
                    if (fontColorPicker) {
                        fontColorPicker.value = color;
                        if (this.textHandler) {
                            this.textHandler.updateColorPreview(fontColorPicker, color);
                        }
                        // Update color value display
                        this.updateColorValueDisplay(fontColorPicker, color);
                    }
                    break;
                    
                case 'border':
                    const borderColor = document.getElementById('borderColor');
                    if (borderColor) {
                        borderColor.value = color;
                        if (this.textHandler) {
                            this.textHandler.updateColorPreview(borderColor, color);
                            this.textHandler.updateSelectedTextBorder('borderColor', color);
                        }
                        this.updateColorValueDisplay(borderColor, color);
                    }
                    break;
                    
                case 'background':
                    const textBgColor = document.getElementById('textBgColor');
                    if (textBgColor) {
                        textBgColor.value = color;
                        if (this.textHandler) {
                            this.textHandler.updateColorPreview(textBgColor, color);
                            this.textHandler.updateSelectedTextBackground();
                        }
                        this.updateColorValueDisplay(textBgColor, color);
                    }
                    break;
                    
                case 'shadow':
                    const shadowColor = document.getElementById('shadowColor');
                    if (shadowColor) {
                        shadowColor.value = color;
                        if (this.textHandler) {
                            this.textHandler.updateColorPreview(shadowColor, color);
                            this.textHandler.updateTextShadow();
                        }
                        this.updateColorValueDisplay(shadowColor, color);
                    }
                    break;
            }
        } else if (this.selectedElement.classList.contains('kms-image-element') && target === 'imageBorder') {
            // Apply border color to image element
            this.selectedElement.style.borderColor = color;
            
            // Update border color picker if available
            const borderColorPicker = document.getElementById('imageBorderColor');
            if (borderColorPicker) {
                borderColorPicker.value = color;
                this.updateColorValueDisplay(borderColorPicker, color);
            }
        }
    }

    updateColorValueDisplay(colorPicker, color) {
        // Find the color value display element next to the color picker
        const colorRow = colorPicker.closest('.kms-enhanced-color-row');
        if (colorRow) {
            const colorValue = colorRow.querySelector('.kms-color-value');
            if (colorValue) {
                colorValue.textContent = color.toUpperCase();
            }
        }
    }
    
    setupEventListeners() {
        // Language toggle
        const langToggle = document.getElementById('languageToggle');
        if (langToggle) {
            langToggle.addEventListener('click', () => this.toggleLanguage());
        }
        
        // Paper size controls
        const paperSizeOptions = document.querySelectorAll('.kms-paper-option');
        paperSizeOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                const size = e.currentTarget.dataset.size;
                this.changePaperSize(size);
            });
        });
        
        // Add element buttons
        document.getElementById('addTextBtn')?.addEventListener('click', () => this.addTextElement());
        document.getElementById('addImageBtn')?.addEventListener('click', () => this.triggerImageUpload());
        document.getElementById('addQRBtn')?.addEventListener('click', () => this.showQRGenerator());
        
        // Print button
        document.getElementById('printBtn')?.addEventListener('click', () => this.printPoster());
        
        // Canvas click handler
        const canvas = document.getElementById('posterCanvas');
        if (canvas) {
            canvas.addEventListener('click', (e) => this.handleCanvasClick(e));
        }
        
        // Background color
        document.getElementById('bgColor')?.addEventListener('change', (e) => {
            this.changeBackgroundColor(e.target.value);
        });

        // Canvas border options
        const borderOptions = document.querySelectorAll('.kms-canvas-border-option');
        borderOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                const borderType = e.currentTarget.dataset.border;
                this.changeCanvasBorder(borderType);
            });
        });

        // Canvas tools
        document.getElementById('gridToggle')?.addEventListener('click', () => this.toggleGrid());
        document.getElementById('zoomIn')?.addEventListener('click', () => this.zoomCanvas(1.1));
        document.getElementById('zoomOut')?.addEventListener('click', () => this.zoomCanvas(0.9));

        // Paper radius control
        const paperRadius = document.getElementById('paperRadius');
        const paperRadiusValue = document.getElementById('paperRadiusValue');
        if (paperRadius && paperRadiusValue) {
            paperRadius.addEventListener('input', (e) => {
                const radius = e.target.value + 'px';
                paperRadiusValue.textContent = radius;
                this.changePaperRadius(radius);
            });
        }
        
        // Global key handlers
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        
        // Prevent default drag behavior on canvas
        const canvasContainer = document.querySelector('.kms-canvas-container');
        if (canvasContainer) {
            canvasContainer.addEventListener('dragover', (e) => e.preventDefault());
            canvasContainer.addEventListener('drop', (e) => e.preventDefault());
        }
    }
    
    toggleLanguage() {
        this.currentLanguage = this.currentLanguage === 'en' ? 'zh' : 'en';
        this.updateLanguage();
    }
    
    updateLanguage() {
        const t = this.translations[this.currentLanguage];
        
        // Update all translatable elements
        document.querySelectorAll('[data-translate]').forEach(element => {
            const key = element.dataset.translate;
            if (t[key]) {
                if (element.tagName === 'INPUT' && element.type !== 'button') {
                    element.placeholder = t[key];
                } else {
                    element.textContent = t[key];
                }
            }
        });
        
        // Update elements with data-en and data-zh attributes
        document.querySelectorAll('[data-en][data-zh]').forEach(element => {
            const text = this.currentLanguage === 'en' ? element.dataset.en : element.dataset.zh;
            if (text) {
                element.textContent = text;
            }
        });
        
        // Update title attributes for layer control buttons
        document.querySelectorAll('[data-en-title][data-zh-title]').forEach(element => {
            const title = this.currentLanguage === 'en' ? element.dataset.enTitle : element.dataset.zhTitle;
            if (title) {
                element.title = title;
            }
        });
        
        // Update language toggle button
        const langToggle = document.getElementById('languageToggle');
        if (langToggle) {
            langToggle.textContent = this.currentLanguage === 'en' ? '中文' : 'English';
        }
        
        // Update layer manager language if available
        if (this.layerManager) {
            this.layerManager.updateLayerControls();
        }
        
        // Update quick colors display
        this.updateQuickColorDisplay();
    }
    
    changePaperSize(size) {
        this.currentPaperSize = size;
        const canvas = document.getElementById('posterCanvas');
        
        // Remove existing size classes
        canvas.classList.remove('kms-canvas-letter', 'kms-canvas-4x6');
        
        // Add new size class
        canvas.classList.add(`kms-canvas-${size}`);
        
        // Update active paper option
        document.querySelectorAll('.kms-paper-option').forEach(option => {
            option.classList.toggle('active', option.dataset.size === size);
        });
        
        // Reposition elements if they're outside new bounds
        this.repositionElementsInBounds();
    }
    
    setupCanvas() {
        const canvas = document.getElementById('posterCanvas');
        console.log('Canvas element found:', canvas);
        if (!canvas) {
            console.error('Canvas element not found!');
            return;
        }
        
        // Set initial paper size
        canvas.classList.add('kms-canvas-letter');
        console.log('Canvas classes after setup:', canvas.className);
        
        // Mark first paper option as active
        const firstOption = document.querySelector('.kms-paper-option[data-size="letter"]');
        if (firstOption) {
            firstOption.classList.add('active');
            console.log('Paper option activated:', firstOption);
        } else {
            console.warn('Paper option not found');
        }
        
        // Force canvas to be visible
        canvas.style.display = 'block';
        canvas.style.visibility = 'visible';
        canvas.style.opacity = '1';
        
        console.log('Canvas setup completed');
    }
    
    loadFonts() {
        // Load Google Fonts for additional options (optional)
        const fonts = [
            'Roboto:300,400,500,700',
            'Open+Sans:300,400,600,700'
        ];

        const link = document.createElement('link');
        link.href = `https://fonts.googleapis.com/css2?${fonts.map(f => `family=${f}`).join('&')}&display=swap`;
        link.rel = 'stylesheet';
        document.head.appendChild(link);
    }
    
    addTextElement() {
        // Use text handler if available
        if (this.textHandler) {
            const textElement = this.textHandler.createTextElement();
            if (textElement) {
                this.selectElement(textElement);
            }
            return;
        }

        // Fallback implementation
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;

        const textElement = document.createElement('div');
        textElement.className = 'kms-canvas-element kms-text-element';
        textElement.contentEditable = true;
        textElement.textContent = this.currentLanguage === 'en' ? 'Double click to edit' : '雙擊編輯文字';
        textElement.style.left = '50px';
        textElement.style.top = '50px';
        textElement.style.fontSize = '24px';
        textElement.style.fontFamily = 'Roboto, sans-serif';
        textElement.style.color = '#333333';
        textElement.dataset.elementId = `text_${++this.elementCounter}`;

        // Use addElementToCanvas which handles layer management
        this.addElementToCanvas(textElement);
    }
    
    setupElementInteraction(element) {
        // Mouse down for dragging
        element.addEventListener('mousedown', (e) => this.startDrag(e, element));
        
        // Double click for editing (text elements)
        if (element.classList.contains('kms-text-element')) {
            element.addEventListener('dblclick', (e) => {
                e.stopPropagation();
                element.focus();
            });
        }
        
        // Click to select
        element.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectElement(element);
        });
    }
    
    startDrag(e, element) {
        // Delegate to drag drop handler if available
        if (this.dragDropHandler) {
            return this.dragDropHandler.startDrag(e, element);
        }

        if (e.target.contentEditable === 'true' && e.detail === 2) return; // Don't drag on double-click edit

        e.preventDefault();
        this.isDragging = true;
        this.selectedElement = element;

        const rect = element.getBoundingClientRect();
        const canvasRect = document.getElementById('posterCanvas').getBoundingClientRect();

        // Calculate offset relative to canvas, not viewport
        this.dragOffset = {
            x: e.clientX - canvasRect.left - parseInt(element.style.left || 0),
            y: e.clientY - canvasRect.top - parseInt(element.style.top || 0)
        };

        document.addEventListener('mousemove', this.handleDrag.bind(this));
        document.addEventListener('mouseup', this.stopDrag.bind(this));

        element.style.cursor = 'grabbing';
    }
    
    handleDrag(e) {
        if (!this.isDragging || !this.selectedElement) return;

        const canvas = document.getElementById('posterCanvas');
        const canvasRect = canvas.getBoundingClientRect();

        // Calculate new position relative to canvas
        let newX = e.clientX - canvasRect.left - this.dragOffset.x;
        let newY = e.clientY - canvasRect.top - this.dragOffset.y;

        // Get element dimensions from style or computed values
        const elementWidth = parseInt(this.selectedElement.style.width) || this.selectedElement.offsetWidth;
        const elementHeight = parseInt(this.selectedElement.style.height) || this.selectedElement.offsetHeight;

        // Constrain to canvas bounds
        newX = Math.max(0, Math.min(newX, canvas.offsetWidth - elementWidth));
        newY = Math.max(0, Math.min(newY, canvas.offsetHeight - elementHeight));

        this.selectedElement.style.left = newX + 'px';
        this.selectedElement.style.top = newY + 'px';
    }
    
    stopDrag() {
        this.isDragging = false;
        if (this.selectedElement) {
            this.selectedElement.style.cursor = 'move';
        }
        document.removeEventListener('mousemove', this.handleDrag);
        document.removeEventListener('mouseup', this.stopDrag);
    }
    
    selectElement(element) {
        // Remove selection from all elements
        document.querySelectorAll('.kms-canvas-element').forEach(el => {
            el.classList.remove('selected');
        });
        
        // Select the clicked element
        element.classList.add('selected');
        this.selectedElement = element;
        
        // Update controls based on element type
        this.updateControlsForElement(element);
        
        // 更新分層控制
        if (this.layerManager) {
            this.layerManager.updateLayerControls();
            this.layerManager.showLayerControls();
        }
    }
    
    hideAllControls() {
        const textControlsSection = document.getElementById('textControlsSection');
        const imageControlsSection = document.getElementById('imageControlsSection');
        const qrControlsSection = document.getElementById('qrControlsSection');
        // Layer Controls should always be visible, so we don't hide it here

        if (textControlsSection) textControlsSection.style.display = 'none';
        if (imageControlsSection) imageControlsSection.style.display = 'none';
        if (qrControlsSection) qrControlsSection.style.display = 'none';
    }
    
    updateControlsForElement(element) {
        // Hide all control sections first
        this.hideAllControls();

        // Layer Controls should always be visible in right panel
        const rightLayerControlsSection = document.getElementById('rightLayerControlsSection');
        if (rightLayerControlsSection) {
            rightLayerControlsSection.style.display = 'block';
        }

        // Show relevant controls based on element type
        const textControlsSection = document.getElementById('textControlsSection');
        const imageControlsSection = document.getElementById('imageControlsSection');
        const qrControlsSection = document.getElementById('qrControlsSection');

        if (element.classList.contains('kms-text-element')) {
            if (textControlsSection) {
                textControlsSection.style.display = 'block';
                textControlsSection.classList.remove('kms-hidden-section');
            }
            if (this.textHandler) {
                this.textHandler.updateControlsForTextElement(element);
            }
        } else if (element.classList.contains('kms-image-element')) {
            if (imageControlsSection) {
                imageControlsSection.style.display = 'block';
                imageControlsSection.classList.remove('kms-hidden-section');
            }
            if (this.imageHandler) {
                this.imageHandler.updateControlsForImageElement(element);
            }
        } else if (element.classList.contains('kms-qr-element')) {
            if (qrControlsSection) {
                qrControlsSection.style.display = 'block';
                qrControlsSection.classList.remove('kms-hidden-section');
            }
            if (this.qrGenerator) {
                this.qrGenerator.updateControlsForQRElement(element);
            }
        }
    }
    
    handleCanvasClick(e) {
        if (e.target.id === 'posterCanvas') {
            // Clicked on empty canvas - deselect all
            this.selectedElement = null;
            document.querySelectorAll('.kms-canvas-element').forEach(el => {
                el.classList.remove('selected');
            });

            // Update layer controls but keep them visible
            if (this.layerManager) {
                this.layerManager.updateLayerControls();
                this.layerManager.updateLayerList();
            }

            // Hide element-specific controls
            this.hideAllControls();
        }
    }
    
    handleKeyDown(e) {
        if (e.key === 'Delete' && this.selectedElement) {
            this.deleteSelectedElement();
        }
    }
    
    deleteSelectedElement() {
        if (this.selectedElement) {
            const elementToRemove = this.selectedElement;
            elementToRemove.remove();
            this.elements = this.elements.filter(el => el !== elementToRemove);
            
            // 通知分層管理器元素已移除
            if (this.layerManager) {
                this.layerManager.onElementRemoved(elementToRemove);
            }
            
            this.selectedElement = null;
        }
    }
    
    repositionElementsInBounds() {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;
        
        this.elements.forEach(element => {
            const rect = element.getBoundingClientRect();
            const canvasRect = canvas.getBoundingClientRect();
            
            let left = parseInt(element.style.left) || 0;
            let top = parseInt(element.style.top) || 0;
            
            // Adjust if outside bounds
            if (left + rect.width > canvas.offsetWidth) {
                left = canvas.offsetWidth - rect.width;
            }
            if (top + rect.height > canvas.offsetHeight) {
                top = canvas.offsetHeight - rect.height;
            }
            
            element.style.left = Math.max(0, left) + 'px';
            element.style.top = Math.max(0, top) + 'px';
        });
    }
    
    changeBackgroundColor(color) {
        const canvas = document.getElementById('posterCanvas');
        if (canvas) {
            canvas.style.backgroundColor = color;
        }
    }
    
    triggerImageUpload() {
        if (this.imageHandler) {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = (e) => {
                if (e.target.files[0]) {
                    this.imageHandler.handleImageUpload(e.target.files[0]);
                }
            };
            input.click();
        }
    }

    showQRGenerator() {
        if (this.qrGenerator) {
            const qrControlsSection = document.getElementById('qrControlsSection');
            if (qrControlsSection) {
                qrControlsSection.style.display = 'block';
                qrControlsSection.classList.remove('kms-hidden-section');
                const urlInput = document.getElementById('qrUrl');
                if (urlInput) {
                    urlInput.focus();
                }
            }
        }
    }
    
    printPoster() {
        if (this.printHandler) {
            this.printHandler.printPoster();
        } else {
            window.print();
        }
    }

    changeCanvasBorder(borderType) {
        const canvas = document.getElementById('posterCanvas');
        const borderElement = document.getElementById('canvasBorder');

        if (!canvas || !borderElement) return;

        // Remove existing border classes
        borderElement.className = 'kms-canvas-border';

        // Add new border class if not 'none'
        if (borderType !== 'none') {
            borderElement.classList.add(`kms-border-${borderType}`);
        }

        // Update active border option
        document.querySelectorAll('.kms-canvas-border-option').forEach(option => {
            option.classList.toggle('active', option.dataset.border === borderType);
        });
    }

    toggleGrid() {
        const grid = document.getElementById('canvasGrid');
        const gridToggle = document.getElementById('gridToggle');

        if (grid && gridToggle) {
            grid.classList.toggle('active');
            gridToggle.classList.toggle('active');
        }
    }

    zoomCanvas(factor) {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;

        const currentTransform = canvas.style.transform || 'scale(1)';
        const currentScale = parseFloat(currentTransform.match(/scale\(([^)]+)\)/)?.[1] || 1);
        const newScale = Math.max(0.5, Math.min(2, currentScale * factor));

        canvas.style.transform = `scale(${newScale})`;
        canvas.style.transformOrigin = 'center center';
    }

    changePaperRadius(radius) {
        const canvas = document.getElementById('posterCanvas');
        if (canvas) {
            canvas.style.borderRadius = radius;
        }
    }
    
    /**
     * 初始化分層管理器
     */
    initializeLayerManager() {
        if (typeof KMSLayerManager !== 'undefined') {
            this.layerManager = new KMSLayerManager(this);
            // 確保 Layer Controls 始終顯示
            this.layerManager.showLayerControls();
        } else {
            console.warn('KMSLayerManager not found. Layer management features will be disabled.');
        }
    }
    
    /**
     * 添加元素到畫布（通用方法）
     */
    addElementToCanvas(element) {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;
        
        this.setupElementInteraction(element);
        canvas.appendChild(element);
        this.elements.push(element);
        
        // 通知分層管理器有新元素添加
        if (this.layerManager) {
            this.layerManager.onElementAdded(element);
        }
        
        this.selectElement(element);
    }
    
    /**
     * 獲取所有元素（供分層管理器使用）
     */
    getAllElements() {
        return this.elements;
    }
    
    /**
     * 重新組織元素z-index
     */
    reorganizeElementLayers() {
        if (this.layerManager) {
            this.layerManager.reorganizeZIndexes();
        }
    }

    /**
     * Initialize enhanced controls for better UX
     */
    initializeEnhancedControls() {
        // Initialize range value displays
        this.initializeRangeDisplays();
        
        // Initialize color value displays
        this.initializeColorValueDisplays();
    }

    initializeRangeDisplays() {
        const rangeInputs = document.querySelectorAll('.kms-enhanced-range');
        rangeInputs.forEach(range => {
            const updateDisplay = () => {
                const displayId = range.id + 'Value';
                const display = document.getElementById(displayId);
                if (display) {
                    let value = range.value;
                    let unit = 'px';
                    
                    // Special handling for opacity
                    if (range.id.includes('Opacity')) {
                        unit = '%';
                    }
                    
                    display.textContent = value + unit;
                }
            };
            
            // Update on input
            range.addEventListener('input', updateDisplay);
            
            // Initialize display
            updateDisplay();
        });
    }

    initializeColorValueDisplays() {
        const colorPickers = document.querySelectorAll('.kms-enhanced-color-picker');
        colorPickers.forEach(picker => {
            const updateDisplay = () => {
                const colorRow = picker.closest('.kms-enhanced-color-row');
                if (colorRow) {
                    const colorValue = colorRow.querySelector('.kms-color-value');
                    if (colorValue) {
                        colorValue.textContent = picker.value.toUpperCase();
                    }
                }
            };
            
            // Update on change
            picker.addEventListener('change', updateDisplay);
            picker.addEventListener('input', updateDisplay);
            
            // Initialize display
            updateDisplay();
        });
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.kmsPosterMaker = new KMSPosterMaker();
});